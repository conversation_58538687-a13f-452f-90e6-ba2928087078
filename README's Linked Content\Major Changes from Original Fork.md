Under construction... <PERSON>, je corrige des trucs, Sur le document existant, mais évidemment je parle en français vu que c'est ma langue natale, ça va plus vite Même si le document a pour vocation d'être en anglais.
Pour le moment, on va construire, ça n'a pas pour but d'être une version finale Le but est de rassembler un maximum d'explications sur tous les comportements introduits Et changement évidemment par rapport à avant.

Bon je dicte pas mal au logiciel de reconnaissance vocale donc il y a quelques fautes Acceptables....
Bon, il y a les autres Fichier d'aide readme et dans readme's linked content. Toute l'information est complètement dispatchée. Là, j'ajoute des informations que je dicte au au fur et à mesure Ça n'est peut être ni à au bon endroit et ça casse l'organisation, mais c'est juste que je donne de l'information brute. Après il faudra évidemment reconstruire. En comparant évidemment à la structure actuelle du Du projet. 

Il va falloir remettre à jour ce document en faisant un diff avec la branche old vs la branche main. Avant le titre de niveau 1, juste en dessous, j'ajouterai, de façon random, des choses que je vois qui ont différé d'avant.

# 🚀 Major Changes from Original Fork

This document outlines the significant architectural and functional changes made to Writing Tools compared to the original forked version (now in the `old` branch). [Ouais enfin ça c'est entre nous Et ça sera pas dans le résultat final, ces histoires de branches...]

## 🏗️ Architecture Overhaul

### **Multi-Mode System**
- **Original**: Single development mode et command to build an executable. Aucun des fichiers Nécessaire au build de finale n'était collé automatiquement ou quoi que ce soit. Il fallait retirer soi-même quelques fichiers car ça n'était pas dans le gitignore.

[Je vais ajouter des trucs un peu Random qui me viennent là Totalement rien à voir avec le contexte, mais ça me vient donc Déjà certaines fenêtres avaient un système de 2 scrolls Très non fonctionnel Enfin très mauvais Bon c'est pas le mot qu'il faudra employer mais c'est ce qui me vient Ensuite, donc ça, ça pouvait aussi déplacer d'autres sous-éléments quand on scrollait Donc ça a changé le contenu, par exemple des dropdown. Ce qui peut être très très problématique D'ailleurs, en pensant à ça, tous les changements n'étaient pas en temps réel Alors que maintenant, si on change Le mode light Dark Le Background Si on change une option Tout est enregistré automatiquement Et s'il y a un résultat, il est visible automatiquement Enfin, si un résultat dans la fenêtre actuelle Ça permet d'être beaucoup plus précis dans ce qu'on désire Comme modification de variable ou d'interface D'ailleurs, cette option de Mode light, Dark auto a été ajoutée. Tous les thèmes ont été revus pour correspondre à ces modes Aussi bien si on est en mode Dark plane ou Dark gradients Enfin, je veux dire une combinaison des Le fonds est le thème fin, ça s'appelle le thème en fait, mais bon je sais pas. Enfin on peut combiner les plusieurs paramètres et voir le résultat et tout a été revu et corrigé et il y a pas de bug Enfin, tout a été vérifié. Bon, je parle vraiment de ce qui me vient, ce qui a rien à voir avec le document général, mais c'est pas grave comme ça, ça sera écrit quelque part... Il faut, quand il y a pas de configuration, donc toujours valider les 2 premières fenêtres de réglage. Mais si on oublie de rentrer une clé à API, ça ne va pas refermer l'application. Y aura juste une erreur signalée si on essaye de lancer l'application sans modèle de LLM valable D'ailleurs, y a tout un tas de d'erreurs au cas où. Euh On peut pas atteindre le LLM ou si on a fait trop de requêtes, et cetera. Il faut remarquer Que olamas Ah, dans son drop down maintenant automatiquement Les Modèle installés. Donc plus besoin de s'embêter à chercher les noms. A propos, j'ai ajouté Mistral avec un modèle par défaut gratuit, sans limitation qui fonctionne comme un charme Et Claude Mais je n'ai pas pu tester Claude Car je ne dispose pas de crédit gratuit. On peut à présent utiliser les touches Méta ou Win Bien qu'il n'y ait une imitation, un petit bug sous Windows Ça fait flasher l'écran. C'est gênant, mais une fois habitué, c'est pas rédhibitoire. Il faudra expliquer les changements qu'on a faits sur les différents pop-up Par exemple, on a introduit le mode force pour obliger à aller Vers le chat Donc c'est pratique parce que si on a du texte sélectionné malgré qu'une commande, enfin une une action, c'est à dire un bouton d'action a été configuré pour ajouter Une fonction est qu'elle est en bas, cochée juste Edit On peut tout de même la forcer à se lancer dans une fenêtre. Donc, par exemple, des fois on a du texte sélectionné, on veut pas forcément le changer, on veut juste avoir par exemple sa traduction et Ben on peut par exemple là dans VSC si je sélectionne du texte j'ai pas envie de le changer. Mais peut-être que je vais avoir sa traduction Ensuite, si on n'a pas de texte sélectionné, le chat apparaît. Il y a un bouton maintenant Sur chaque réponse pour copier en format markdown. Bon, comme j'ai déjà dit avant les fenêtres ont été modernisées Une mise en forme plus intelligente. Des scroll bar fixés ou qui apparaissent car on peut redimensionner certaines fenêtres. Par exemple, les fenêtres de configuration tout au départ sont maintenant rétrécissables. Ou peuvent perdre le focus C'est quand même tout de même pratique de pouvoir voir le contenu de son desktop si on veut rentrer une API key. Donc les fenêtres apparaissent on top au départ Et ensuite, elles peuvent perdre le focus pour Faire d'autres choses si, par exemple, des fois on a besoin immédiatement de voir quelque chose sur son écran. Ouais donc concernant la fenêtre custom pop-up Windows Expliquer le rôle du cadenas et de force chat. On a donc vu avec du texte sélectionné Mais ce que j'ai pas dit, c'est que si, par exemple, on est dans une fenêtre. Internet On peut sélectionner du texte, mais le texte n'est pas équitable. À ce moment là L'application passe maintenant automatiquement à une fenêtre Non, éditable modal.  Ça permet donc de faire des choses Tout de même dans J'ai une page web Ou sur du texte non éditable Comme par exemple la traduction Les autres boutons sont toujours disponibles. Bon je teste hein, paresse que je dis effectivement Effectivement la touche Windows est très problématique Mais c'est signalé Ça pourrait être et facilement utile sous Linux Avec CMD. Les appels Avec les boutons lorsque on prend du texte sélectionné non éditable Peuvent être un peu longs. Donc préférer la zone de chat. Bon, les moteurs ont été update Et Gemini propose par exemple Gemini 25 pro Flash Par défaut ? Enfin, je sais plus le nom exact, mais bref.]

- **New**: Three distinct modes with automatic detection
  - `dev`: Development mode (source code)
  - `build-dev`: Development build (executable with dev settings)
  - `build-final`: Production build (executable with final settings)

### **Settings Management Revolution**
- **Original**: Basic configuration handling
- **New**: Sophisticated `SettingsManager` with:
  - Mode-aware configuration loading
  - Automatic migration between versions
  - Separate dev/production settings files
  - Robust error handling and validation
  - Dynamic provider configuration

### **Build System Modernization**
- **Original**: Manual PyInstaller commands
- **New**: Automated build scripts with:
  - `scripts/dev_build.py` - Fast development builds
  - `scripts/final_build.py` - Production builds
  - `scripts/dev_script.py` - Direct source execution
  - Console mode support (`--console` flag)
  - Automatic environment setup
  - Dependency management

## 🔧 Development Experience

### **Enhanced Debugging**
- **Console Mode**: Real-time log visibility during development
- **Startup Debug**: Specialized tools for systray issues
- **Detailed Logging**: Comprehensive error tracking and diagnostics
- **Environment Detection**: Automatic mode switching based on context

### **Improved Scripts**
- **Location**: All scripts moved to `Windows_and_Linux/scripts/`
- **Functionality**: 
  - `startup_debug.py` - Debug systray startup issues
  - `install_startup_debug.py` - Auto-debug at Windows boot
  - `update_deps.py` - Dependency management
  - `utils.py` - Shared utilities

### **Code Runner Integration**
- **Original**: Manual command execution
- **New**: Optimized for VS Code Code Runner
- **Simple Commands**:
  - `python scripts/dev_script.py` - Run from source
  - `python scripts/dev_build.py` - Build and run
  - `python scripts/dev_build.py --console` - Debug build
  - `python scripts/final_build.py` - Production build

## 🎯 User Experience

### **Systray Reliability**
- **Enhanced Detection**: Better system tray availability checking
- **Retry Mechanisms**: Automatic retry for systray creation
- **Startup Debugging**: Tools to diagnose boot-time issues
- **Cross-Platform**: Improved Windows compatibility

### **Provider System**
- **Dynamic Loading**: Providers loaded based on availability
- **Error Handling**: Graceful fallbacks when providers fail
- **Configuration**: Per-mode provider settings
- **Validation**: API key and endpoint validation

## 📁 File Organization

### **Cleaned Structure**
- **Scripts**: Consolidated in `scripts/` directory
- **Documentation**: Streamlined in `README's Linked Content/`
- **Logs**: Temporary files automatically cleaned
- **Assets**: Proper asset management and copying

### **Removed Clutter**
- Eliminated temporary test files
- Removed duplicate batch/PowerShell scripts
- Cleaned up development artifacts
- Consolidated documentation

## 🔄 Migration Path

### **From Original to New**
1. **Settings**: Automatic migration of existing configurations
2. **Providers**: API keys preserved during upgrade
3. **Preferences**: UI settings maintained
4. **Compatibility**: Backward compatibility where possible

### **Development Workflow**
1. **Clone**: Use this enhanced version
2. **Setup**: Run `python scripts/dev_script.py` for immediate testing
3. **Build**: Use `python scripts/dev_build.py` for executable testing
4. **Debug**: Add `--console` flag when issues arise
5. **Deploy**: Use `python scripts/final_build.py` for distribution

## 🎯 Why These Changes?

### **Scalability**
The original codebase was difficult to maintain and extend. The new architecture supports:
- Easy addition of new AI providers
- Simplified debugging and troubleshooting
- Better separation of concerns
- Automated testing and deployment

### **Developer Experience**
- **Faster Iteration**: Quick development builds
- **Better Debugging**: Console mode and detailed logging
- **Simplified Workflow**: One-command build and run
- **Documentation**: Clear usage instructions

### **User Reliability**
- **Robust Startup**: Better handling of Windows systray issues
- **Error Recovery**: Graceful handling of provider failures
- **Performance**: Optimized builds with unnecessary modules excluded
- **Maintenance**: Easier updates and configuration management

## 🚀 Future Roadmap

This enhanced architecture provides a solid foundation for:
- Additional AI provider integrations
- Advanced UI features
- Cross-platform expansion
- Plugin system development
- Automated testing framework

The changes represent a complete modernization while maintaining the core functionality that users expect from Writing Tools.
